import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { UpdateHabitData, ApiResponse, HabitWithCategory } from '@/types'

interface RouteParams {
  params: {
    id: string
  }
}

// GET /api/habits/[id] - Get a specific habit
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const habit = await prisma.habit.findFirst({
      where: {
        id: params.id,
        userId: session.user.id
      },
      include: {
        category: true,
        completions: {
          orderBy: { completedAt: 'desc' },
          take: 30 // Get last 30 completions for detailed view
        },
        streaks: {
          orderBy: { startDate: 'desc' }
        }
      }
    })

    if (!habit) {
      return NextResponse.json(
        { success: false, error: 'Habit not found' },
        { status: 404 }
      )
    }

    const response: ApiResponse<HabitWithCategory> = {
      success: true,
      data: habit
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('Error fetching habit:', error)
    
    const response: ApiResponse = {
      success: false,
      error: 'Failed to fetch habit'
    }

    return NextResponse.json(response, { status: 500 })
  }
}

// PUT /api/habits/[id] - Update a habit
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body: UpdateHabitData & { category?: string } = await request.json()

    // Check if habit exists and belongs to user
    const existingHabit = await prisma.habit.findFirst({
      where: {
        id: params.id,
        userId: session.user.id
      }
    })

    if (!existingHabit) {
      return NextResponse.json(
        { success: false, error: 'Habit not found' },
        { status: 404 }
      )
    }

    // Validate frequency if provided
    if (body.frequency && !['DAILY', 'WEEKLY', 'MONTHLY'].includes(body.frequency)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid frequency. Must be DAILY, WEEKLY, or MONTHLY' 
        },
        { status: 400 }
      )
    }

    // Validate difficulty if provided
    if (body.difficulty && !['EASY', 'MEDIUM', 'HARD'].includes(body.difficulty)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid difficulty. Must be EASY, MEDIUM, or HARD' 
        },
        { status: 400 }
      )
    }

    // Find category if provided
    let categoryId = existingHabit.categoryId
    if (body.category !== undefined) {
      if (body.category) {
        const category = await prisma.habitCategory.findFirst({
          where: { name: body.category }
        })
        categoryId = category?.id || null
      } else {
        categoryId = null
      }
    }

    // Update the habit
    const updatedHabit = await prisma.habit.update({
      where: { id: params.id },
      data: {
        ...(body.title && { title: body.title.trim() }),
        ...(body.description !== undefined && { 
          description: body.description?.trim() || null 
        }),
        ...(body.frequency && { frequency: body.frequency }),
        ...(body.difficulty !== undefined && { difficulty: body.difficulty }),
        ...(body.isActive !== undefined && { isActive: body.isActive }),
        categoryId,
        updatedAt: new Date()
      },
      include: {
        category: true,
        completions: true,
        streaks: true
      }
    })

    const response: ApiResponse<HabitWithCategory> = {
      success: true,
      data: updatedHabit,
      message: 'Habit updated successfully'
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('Error updating habit:', error)
    
    const response: ApiResponse = {
      success: false,
      error: 'Failed to update habit'
    }

    return NextResponse.json(response, { status: 500 })
  }
}

// DELETE /api/habits/[id] - Delete a habit
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check if habit exists and belongs to user
    const existingHabit = await prisma.habit.findFirst({
      where: {
        id: params.id,
        userId: session.user.id
      }
    })

    if (!existingHabit) {
      return NextResponse.json(
        { success: false, error: 'Habit not found' },
        { status: 404 }
      )
    }

    // Delete the habit (this will cascade delete completions and streaks)
    await prisma.habit.delete({
      where: { id: params.id }
    })

    const response: ApiResponse = {
      success: true,
      message: 'Habit deleted successfully'
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('Error deleting habit:', error)
    
    const response: ApiResponse = {
      success: false,
      error: 'Failed to delete habit'
    }

    return NextResponse.json(response, { status: 500 })
  }
}
