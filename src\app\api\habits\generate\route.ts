import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { generateHabits } from '@/lib/gemini'
import { prisma } from '@/lib/prisma'
import { HabitGenerationRequest, ApiResponse, GeneratedHabit } from '@/types'

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Parse request body
    const body: HabitGenerationRequest = await request.json()
    
    // Validate required fields
    if (!body.userDescription || body.userDescription.trim().length < 20) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'User description is required and must be at least 20 characters long' 
        },
        { status: 400 }
      )
    }

    // Save or update user profile
    await prisma.userProfile.upsert({
      where: { userId: session.user.id },
      update: {
        description: body.userDescription,
        goals: body.goals || null,
        lifestyle: body.lifestyle || null,
        preferences: body.preferences || null,
        updatedAt: new Date()
      },
      create: {
        userId: session.user.id,
        description: body.userDescription,
        goals: body.goals || null,
        lifestyle: body.lifestyle || null,
        preferences: body.preferences || null
      }
    })

    // Generate habits using Gemini AI
    const generatedHabits = await generateHabits(body)

    // Return the generated habits
    const response: ApiResponse<GeneratedHabit[]> = {
      success: true,
      data: generatedHabits,
      message: `Generated ${generatedHabits.length} personalized habits`
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('Error in habit generation API:', error)
    
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to generate habits'
    }

    return NextResponse.json(response, { status: 500 })
  }
}
