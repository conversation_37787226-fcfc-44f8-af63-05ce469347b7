import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

const HABIT_CATEGORIES = [
  {
    name: 'Health',
    description: 'General health and wellness habits',
    color: '#10B981', // green
    icon: '🏥'
  },
  {
    name: 'Fitness',
    description: 'Physical exercise and activity habits',
    color: '#F59E0B', // amber
    icon: '💪'
  },
  {
    name: 'Nutrition',
    description: 'Diet and eating habits',
    color: '#84CC16', // lime
    icon: '🥗'
  },
  {
    name: 'Sleep',
    description: 'Sleep quality and schedule habits',
    color: '#6366F1', // indigo
    icon: '😴'
  },
  {
    name: 'Mindfulness',
    description: 'Mental health and mindfulness practices',
    color: '#8B5CF6', // violet
    icon: '🧘'
  },
  {
    name: 'Productivity',
    description: 'Work efficiency and organization habits',
    color: '#EF4444', // red
    icon: '⚡'
  },
  {
    name: 'Learning',
    description: 'Education and skill development habits',
    color: '#3B82F6', // blue
    icon: '📚'
  },
  {
    name: 'Social',
    description: 'Relationships and social interaction habits',
    color: '#EC4899', // pink
    icon: '👥'
  },
  {
    name: 'Career',
    description: 'Professional development and career habits',
    color: '#6B7280', // gray
    icon: '💼'
  },
  {
    name: 'Creativity',
    description: 'Creative expression and artistic habits',
    color: '#F97316', // orange
    icon: '🎨'
  }
]

async function main() {
  console.log('🌱 Seeding database...')

  // Create habit categories
  console.log('Creating habit categories...')
  for (const category of HABIT_CATEGORIES) {
    await prisma.habitCategory.upsert({
      where: { name: category.name },
      update: {
        description: category.description,
        color: category.color,
        icon: category.icon
      },
      create: category
    })
  }

  console.log('✅ Database seeded successfully!')
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
