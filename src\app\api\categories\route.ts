import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { ApiResponse } from '@/types'
import { HabitCategory } from '@prisma/client'

// GET /api/categories - Get all habit categories
export async function GET(request: NextRequest) {
  try {
    const categories = await prisma.habitCategory.findMany({
      orderBy: { name: 'asc' }
    })

    const response: ApiResponse<HabitCategory[]> = {
      success: true,
      data: categories
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('Error fetching categories:', error)
    
    const response: ApiResponse = {
      success: false,
      error: 'Failed to fetch categories'
    }

    return NextResponse.json(response, { status: 500 })
  }
}
