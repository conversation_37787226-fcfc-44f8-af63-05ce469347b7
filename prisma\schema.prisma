// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

// User model for authentication and profile
model User {
  id            String    @id @default(cuid())
  email         String    @unique
  name          String?
  image         String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  profile       UserProfile?
  habits        Habit[]
  completions   HabitCompletion[]
  streaks       Streak[]

  @@map("users")
}

// Extended user profile for AI habit generation
model UserProfile {
  id          String   @id @default(cuid())
  userId      String   @unique
  description String?  // User's self-description for AI generation
  goals       String?  // User's goals and aspirations
  lifestyle   String?  // Lifestyle information
  preferences Json?    // Additional preferences as <PERSON><PERSON><PERSON>
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_profiles")
}

// Habit categories for organization
model HabitCategory {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  color       String?  // Hex color for UI
  icon        String?  // Icon identifier
  createdAt   DateTime @default(now())

  // Relations
  habits      Habit[]

  @@map("habit_categories")
}

// Main habits model
model Habit {
  id          String      @id @default(cuid())
  userId      String
  title       String
  description String?
  frequency   Frequency   // DAILY, WEEKLY, MONTHLY
  difficulty  Difficulty? // EASY, MEDIUM, HARD
  isActive    Boolean     @default(true)
  isAiGenerated Boolean   @default(false)
  categoryId  String?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  user        User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  category    HabitCategory?    @relation(fields: [categoryId], references: [id])
  completions HabitCompletion[]
  streaks     Streak[]

  @@map("habits")
}

// Habit completion tracking
model HabitCompletion {
  id          String   @id @default(cuid())
  habitId     String
  userId      String
  completedAt DateTime @default(now())
  notes       String?

  // Relations
  habit       Habit    @relation(fields: [habitId], references: [id], onDelete: Cascade)
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([habitId, completedAt])
  @@map("habit_completions")
}

// Streak tracking for habits
model Streak {
  id          String   @id @default(cuid())
  habitId     String
  userId      String
  startDate   DateTime
  endDate     DateTime?
  length      Int      @default(1)
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  habit       Habit    @relation(fields: [habitId], references: [id], onDelete: Cascade)
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("streaks")
}

// Enums
enum Frequency {
  DAILY
  WEEKLY
  MONTHLY
}

enum Difficulty {
  EASY
  MEDIUM
  HARD
}
