'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Textarea } from '@/components/ui/Textarea'
import { Label } from '@/components/ui/Label'
import { HabitGenerationRequest } from '@/types'

interface UserProfileFormProps {
  onSubmit: (data: HabitGenerationRequest) => void
  isLoading?: boolean
}

const HABIT_CATEGORIES = [
  'Health', 'Productivity', 'Mindfulness', 'Learning', 
  'Social', 'Fitness', 'Nutrition', 'Sleep', 'Career', 'Creativity'
]

export function UserProfileForm({ onSubmit, isLoading = false }: UserProfileFormProps) {
  const [formData, setFormData] = useState<HabitGenerationRequest>({
    userDescription: '',
    goals: '',
    lifestyle: '',
    preferences: {
      frequency: undefined,
      difficulty: undefined,
      categories: []
    }
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (formData.userDescription.trim()) {
      onSubmit(formData)
    }
  }

  const handleCategoryToggle = (category: string) => {
    const currentCategories = formData.preferences?.categories || []
    const updatedCategories = currentCategories.includes(category)
      ? currentCategories.filter(c => c !== category)
      : [...currentCategories, category]
    
    setFormData(prev => ({
      ...prev,
      preferences: {
        ...prev.preferences,
        categories: updatedCategories
      }
    }))
  }

  return (
    <div className="max-w-2xl mx-auto bg-white p-8 rounded-lg shadow-lg">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Tell us about yourself
        </h2>
        <p className="text-gray-600">
          Help our AI understand you better to generate personalized habits that fit your lifestyle.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* User Description */}
        <div>
          <Label htmlFor="description" className="text-gray-700 mb-2 block">
            Describe yourself <span className="text-red-500">*</span>
          </Label>
          <Textarea
            id="description"
            placeholder="Tell us about your current lifestyle, interests, challenges, and what you'd like to improve. For example: 'I'm a busy software developer who works from home. I struggle with maintaining a healthy work-life balance and want to be more active and mindful.'"
            value={formData.userDescription}
            onChange={(e) => setFormData(prev => ({ ...prev, userDescription: e.target.value }))}
            className="min-h-[120px]"
            required
          />
          <p className="text-sm text-gray-500 mt-1">
            Be specific about your current situation and challenges (minimum 20 characters)
          </p>
        </div>

        {/* Goals */}
        <div>
          <Label htmlFor="goals" className="text-gray-700 mb-2 block">
            What are your main goals?
          </Label>
          <Textarea
            id="goals"
            placeholder="What do you want to achieve? For example: 'I want to improve my physical health, reduce stress, be more productive at work, and develop better sleep habits.'"
            value={formData.goals}
            onChange={(e) => setFormData(prev => ({ ...prev, goals: e.target.value }))}
            className="min-h-[80px]"
          />
        </div>

        {/* Lifestyle */}
        <div>
          <Label htmlFor="lifestyle" className="text-gray-700 mb-2 block">
            Describe your lifestyle
          </Label>
          <Textarea
            id="lifestyle"
            placeholder="Tell us about your daily routine, work schedule, family situation, hobbies, etc. For example: 'I work 9-5 in an office, have two young kids, and enjoy reading in my free time.'"
            value={formData.lifestyle}
            onChange={(e) => setFormData(prev => ({ ...prev, lifestyle: e.target.value }))}
            className="min-h-[80px]"
          />
        </div>

        {/* Preferences */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900">Preferences (Optional)</h3>
          
          {/* Frequency Preference */}
          <div>
            <Label className="text-gray-700 mb-2 block">
              Preferred habit frequency
            </Label>
            <div className="flex gap-2 flex-wrap">
              {(['DAILY', 'WEEKLY', 'MONTHLY'] as const).map((freq) => (
                <button
                  key={freq}
                  type="button"
                  onClick={() => setFormData(prev => ({
                    ...prev,
                    preferences: {
                      ...prev.preferences,
                      frequency: prev.preferences?.frequency === freq ? undefined : freq
                    }
                  }))}
                  className={`px-4 py-2 rounded-md border transition-colors ${
                    formData.preferences?.frequency === freq
                      ? 'bg-blue-500 text-white border-blue-500'
                      : 'bg-white text-gray-700 border-gray-300 hover:border-blue-300'
                  }`}
                >
                  {freq.charAt(0) + freq.slice(1).toLowerCase()}
                </button>
              ))}
            </div>
          </div>

          {/* Difficulty Preference */}
          <div>
            <Label className="text-gray-700 mb-2 block">
              Preferred difficulty level
            </Label>
            <div className="flex gap-2 flex-wrap">
              {(['EASY', 'MEDIUM', 'HARD'] as const).map((diff) => (
                <button
                  key={diff}
                  type="button"
                  onClick={() => setFormData(prev => ({
                    ...prev,
                    preferences: {
                      ...prev.preferences,
                      difficulty: prev.preferences?.difficulty === diff ? undefined : diff
                    }
                  }))}
                  className={`px-4 py-2 rounded-md border transition-colors ${
                    formData.preferences?.difficulty === diff
                      ? 'bg-blue-500 text-white border-blue-500'
                      : 'bg-white text-gray-700 border-gray-300 hover:border-blue-300'
                  }`}
                >
                  {diff.charAt(0) + diff.slice(1).toLowerCase()}
                </button>
              ))}
            </div>
          </div>

          {/* Category Preferences */}
          <div>
            <Label className="text-gray-700 mb-2 block">
              Areas of interest (select multiple)
            </Label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {HABIT_CATEGORIES.map((category) => (
                <button
                  key={category}
                  type="button"
                  onClick={() => handleCategoryToggle(category)}
                  className={`px-3 py-2 text-sm rounded-md border transition-colors ${
                    formData.preferences?.categories?.includes(category)
                      ? 'bg-blue-500 text-white border-blue-500'
                      : 'bg-white text-gray-700 border-gray-300 hover:border-blue-300'
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Submit Button */}
        <div className="pt-4">
          <Button
            type="submit"
            disabled={!formData.userDescription.trim() || isLoading}
            className="w-full"
            size="lg"
          >
            {isLoading ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Generating your personalized habits...
              </div>
            ) : (
              'Generate My Habits with AI 🤖'
            )}
          </Button>
        </div>
      </form>
    </div>
  )
}
