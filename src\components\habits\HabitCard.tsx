'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { GeneratedHabit, HabitWithCategory } from '@/types'
import { cn } from '@/lib/utils'

interface HabitCardProps {
  habit: GeneratedHabit | HabitWithCategory
  onSave?: (habit: GeneratedHabit) => void
  onEdit?: (habit: HabitWithCategory) => void
  onDelete?: (habitId: string) => void
  onToggleComplete?: (habitId: string) => void
  isGenerated?: boolean
  className?: string
}

const FREQUENCY_COLORS = {
  DAILY: 'bg-green-100 text-green-800 border-green-200',
  WEEKLY: 'bg-blue-100 text-blue-800 border-blue-200',
  MONTHLY: 'bg-purple-100 text-purple-800 border-purple-200'
}

const DIFFICULTY_COLORS = {
  EASY: 'bg-emerald-100 text-emerald-700',
  MEDIUM: 'bg-yellow-100 text-yellow-700',
  HARD: 'bg-red-100 text-red-700'
}

const CATEGORY_ICONS: Record<string, string> = {
  Health: '🏥',
  Fitness: '💪',
  Nutrition: '🥗',
  Sleep: '😴',
  Mindfulness: '🧘',
  Productivity: '⚡',
  Learning: '📚',
  Social: '👥',
  Career: '💼',
  Creativity: '🎨'
}

export function HabitCard({ 
  habit, 
  onSave, 
  onEdit, 
  onDelete, 
  onToggleComplete,
  isGenerated = false,
  className 
}: HabitCardProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const handleSave = async () => {
    if (!onSave || !isGenerated) return
    
    setIsLoading(true)
    try {
      await onSave(habit as GeneratedHabit)
    } finally {
      setIsLoading(false)
    }
  }

  const handleToggleComplete = async () => {
    if (!onToggleComplete || isGenerated) return
    
    const habitWithId = habit as HabitWithCategory
    if (habitWithId.id) {
      await onToggleComplete(habitWithId.id)
    }
  }

  const categoryIcon = CATEGORY_ICONS[habit.category || ''] || '📝'
  const frequencyColor = FREQUENCY_COLORS[habit.frequency]
  const difficultyColor = DIFFICULTY_COLORS[habit.difficulty]

  return (
    <div className={cn(
      'bg-white rounded-lg border shadow-sm hover:shadow-md transition-shadow',
      className
    )}>
      <div className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center gap-3">
            <span className="text-2xl">{categoryIcon}</span>
            <div>
              <h3 className="font-semibold text-gray-900 text-lg">
                {habit.title}
              </h3>
              <div className="flex items-center gap-2 mt-1">
                <span className={cn(
                  'px-2 py-1 text-xs font-medium rounded-full border',
                  frequencyColor
                )}>
                  {habit.frequency}
                </span>
                <span className={cn(
                  'px-2 py-1 text-xs font-medium rounded-full',
                  difficultyColor
                )}>
                  {habit.difficulty}
                </span>
                {habit.category && (
                  <span className="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-700">
                    {habit.category}
                  </span>
                )}
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center gap-2">
            {!isGenerated && onToggleComplete && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleToggleComplete}
                className="text-green-600 hover:text-green-700 hover:bg-green-50"
              >
                ✓
              </Button>
            )}
            
            {isGenerated && onSave && (
              <Button
                size="sm"
                onClick={handleSave}
                disabled={isLoading}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {isLoading ? 'Saving...' : 'Save'}
              </Button>
            )}

            {!isGenerated && onEdit && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onEdit(habit as HabitWithCategory)}
              >
                Edit
              </Button>
            )}

            {!isGenerated && onDelete && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const habitWithId = habit as HabitWithCategory
                  if (habitWithId.id) {
                    onDelete(habitWithId.id)
                  }
                }}
                className="text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                Delete
              </Button>
            )}
          </div>
        </div>

        {/* Description */}
        <p className="text-gray-600 mb-3">
          {habit.description}
        </p>

        {/* Reasoning (for generated habits) */}
        {isGenerated && 'reasoning' in habit && habit.reasoning && (
          <div className="mt-3 p-3 bg-blue-50 rounded-md border border-blue-200">
            <p className="text-sm text-blue-800">
              <span className="font-medium">Why this habit:</span> {habit.reasoning}
            </p>
          </div>
        )}

        {/* Expandable details for saved habits */}
        {!isGenerated && (
          <div className="mt-3">
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-sm text-blue-600 hover:text-blue-700 font-medium"
            >
              {isExpanded ? 'Show less' : 'Show details'}
            </button>
            
            {isExpanded && (
              <div className="mt-3 space-y-2 text-sm text-gray-600">
                <div>
                  <span className="font-medium">Created:</span>{' '}
                  {'createdAt' in habit && habit.createdAt 
                    ? new Date(habit.createdAt).toLocaleDateString()
                    : 'Unknown'
                  }
                </div>
                <div>
                  <span className="font-medium">Status:</span>{' '}
                  {'isActive' in habit && habit.isActive ? 'Active' : 'Inactive'}
                </div>
                <div>
                  <span className="font-medium">AI Generated:</span>{' '}
                  {'isAiGenerated' in habit && habit.isAiGenerated ? 'Yes' : 'No'}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
