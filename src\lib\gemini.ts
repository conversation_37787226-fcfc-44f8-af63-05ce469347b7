import { GoogleGenerative<PERSON><PERSON> } from '@google/generative-ai'
import { HabitGenerationRequest, GeneratedHabit } from '@/types'

// Initialize the Gemini AI client
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!)

// Get the Gemini model (using the current available model name)
const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' })

/**
 * Generate personalized habits using Gemini AI
 */
export async function generateHabits(request: HabitGenerationRequest): Promise<GeneratedHabit[]> {
  try {
    const prompt = createHabitGenerationPrompt(request)
    
    const result = await model.generateContent(prompt)
    const response = await result.response
    const text = response.text()
    
    // Parse the AI response into structured habit data
    const habits = parseHabitResponse(text)
    
    return habits
  } catch (error) {
    console.error('Error generating habits with Gemini AI:', error)
    throw new Error('Failed to generate habits. Please try again.')
  }
}

/**
 * Create a detailed prompt for habit generation
 */
function createHabitGenerationPrompt(request: HabitGenerationRequest): string {
  const { userDescription, goals, lifestyle, preferences } = request
  
  const basePrompt = `
You are an expert habit coach and behavioral psychologist. Based on the user's profile, generate 5-8 personalized habits that will help them improve their life.

User Profile:
- Description: ${userDescription}
${goals ? `- Goals: ${goals}` : ''}
${lifestyle ? `- Lifestyle: ${lifestyle}` : ''}

Preferences:
${preferences?.frequency ? `- Preferred frequency: ${preferences.frequency}` : ''}
${preferences?.difficulty ? `- Preferred difficulty: ${preferences.difficulty}` : ''}
${preferences?.categories?.length ? `- Interested categories: ${preferences.categories.join(', ')}` : ''}

Requirements:
1. Generate habits that are specific, measurable, and actionable
2. Include a mix of frequencies (daily, weekly, monthly) unless user specified a preference
3. Vary difficulty levels (easy, medium, hard) to provide progression
4. Make habits relevant to the user's description and goals
5. Provide clear reasoning for why each habit would benefit this user

Please respond with ONLY a valid JSON array in this exact format:
[
  {
    "title": "Habit title (max 50 characters)",
    "description": "Detailed description of what to do (max 200 characters)",
    "frequency": "DAILY" | "WEEKLY" | "MONTHLY",
    "difficulty": "EASY" | "MEDIUM" | "HARD",
    "category": "Health" | "Productivity" | "Mindfulness" | "Learning" | "Social" | "Fitness" | "Nutrition" | "Sleep" | "Career" | "Creativity",
    "reasoning": "Brief explanation of why this habit helps the user (max 150 characters)"
  }
]

Important: Return ONLY the JSON array, no additional text or formatting.
`

  return basePrompt.trim()
}

/**
 * Parse the AI response and extract habit data
 */
function parseHabitResponse(response: string): GeneratedHabit[] {
  try {
    // Clean the response to extract JSON
    const cleanedResponse = response.trim()
    
    // Try to find JSON array in the response
    const jsonMatch = cleanedResponse.match(/\[[\s\S]*\]/)
    if (!jsonMatch) {
      throw new Error('No valid JSON array found in response')
    }
    
    const habits = JSON.parse(jsonMatch[0]) as GeneratedHabit[]
    
    // Validate the parsed habits
    return habits.filter(habit => validateHabit(habit))
  } catch (error) {
    console.error('Error parsing habit response:', error)
    console.error('Raw response:', response)
    
    // Return fallback habits if parsing fails
    return getFallbackHabits()
  }
}

/**
 * Validate a single habit object
 */
function validateHabit(habit: any): boolean {
  return (
    typeof habit.title === 'string' &&
    typeof habit.description === 'string' &&
    ['DAILY', 'WEEKLY', 'MONTHLY'].includes(habit.frequency) &&
    ['EASY', 'MEDIUM', 'HARD'].includes(habit.difficulty) &&
    typeof habit.category === 'string' &&
    habit.title.length > 0 &&
    habit.description.length > 0
  )
}

/**
 * Fallback habits in case AI generation fails
 */
function getFallbackHabits(): GeneratedHabit[] {
  return [
    {
      title: "Morning Hydration",
      description: "Drink a full glass of water immediately after waking up",
      frequency: "DAILY" as const,
      difficulty: "EASY" as const,
      category: "Health",
      reasoning: "Helps kickstart metabolism and rehydrate after sleep"
    },
    {
      title: "Daily Gratitude Journal",
      description: "Write down 3 things you're grateful for each evening",
      frequency: "DAILY" as const,
      difficulty: "EASY" as const,
      category: "Mindfulness",
      reasoning: "Improves mental well-being and positive thinking"
    },
    {
      title: "Weekly Learning Session",
      description: "Spend 1 hour learning something new related to your interests",
      frequency: "WEEKLY" as const,
      difficulty: "MEDIUM" as const,
      category: "Learning",
      reasoning: "Promotes continuous growth and skill development"
    },
    {
      title: "Monthly Goal Review",
      description: "Review and adjust your monthly goals and progress",
      frequency: "MONTHLY" as const,
      difficulty: "MEDIUM" as const,
      category: "Productivity",
      reasoning: "Ensures you stay on track with your objectives"
    }
  ]
}

/**
 * Test the Gemini AI connection
 */
export async function testGeminiConnection(): Promise<boolean> {
  try {
    const result = await model.generateContent("Say 'Hello, Gemini AI is working!'")
    const response = await result.response
    const text = response.text()
    
    return text.includes('Hello') || text.includes('working')
  } catch (error) {
    console.error('Gemini AI connection test failed:', error)
    return false
  }
}
