import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { ApiResponse } from '@/types'

interface RouteParams {
  params: {
    id: string
  }
}

// POST /api/habits/[id]/complete - Mark habit as complete for today
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { notes, completedAt } = body

    // Check if habit exists and belongs to user
    const habit = await prisma.habit.findFirst({
      where: {
        id: params.id,
        userId: session.user.id,
        isActive: true
      }
    })

    if (!habit) {
      return NextResponse.json(
        { success: false, error: 'Habit not found or inactive' },
        { status: 404 }
      )
    }

    const completionDate = completedAt ? new Date(completedAt) : new Date()
    
    // Check if already completed today (for the specific date)
    const startOfDay = new Date(completionDate)
    startOfDay.setHours(0, 0, 0, 0)
    
    const endOfDay = new Date(completionDate)
    endOfDay.setHours(23, 59, 59, 999)

    const existingCompletion = await prisma.habitCompletion.findFirst({
      where: {
        habitId: params.id,
        userId: session.user.id,
        completedAt: {
          gte: startOfDay,
          lte: endOfDay
        }
      }
    })

    if (existingCompletion) {
      return NextResponse.json(
        { success: false, error: 'Habit already completed for this date' },
        { status: 400 }
      )
    }

    // Create completion record
    const completion = await prisma.habitCompletion.create({
      data: {
        habitId: params.id,
        userId: session.user.id,
        completedAt: completionDate,
        notes: notes?.trim() || null
      }
    })

    // Update or create streak
    await updateStreak(params.id, session.user.id, completionDate)

    const response: ApiResponse = {
      success: true,
      data: completion,
      message: 'Habit marked as complete!'
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('Error completing habit:', error)
    
    const response: ApiResponse = {
      success: false,
      error: 'Failed to complete habit'
    }

    return NextResponse.json(response, { status: 500 })
  }
}

// DELETE /api/habits/[id]/complete - Remove completion for today
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const completedAt = searchParams.get('date')

    const targetDate = completedAt ? new Date(completedAt) : new Date()
    
    // Find completion for the specific date
    const startOfDay = new Date(targetDate)
    startOfDay.setHours(0, 0, 0, 0)
    
    const endOfDay = new Date(targetDate)
    endOfDay.setHours(23, 59, 59, 999)

    const completion = await prisma.habitCompletion.findFirst({
      where: {
        habitId: params.id,
        userId: session.user.id,
        completedAt: {
          gte: startOfDay,
          lte: endOfDay
        }
      }
    })

    if (!completion) {
      return NextResponse.json(
        { success: false, error: 'No completion found for this date' },
        { status: 404 }
      )
    }

    // Delete the completion
    await prisma.habitCompletion.delete({
      where: { id: completion.id }
    })

    // Recalculate streak
    await recalculateStreak(params.id, session.user.id)

    const response: ApiResponse = {
      success: true,
      message: 'Habit completion removed'
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('Error removing habit completion:', error)
    
    const response: ApiResponse = {
      success: false,
      error: 'Failed to remove habit completion'
    }

    return NextResponse.json(response, { status: 500 })
  }
}

// Helper function to update streak
async function updateStreak(habitId: string, userId: string, completionDate: Date) {
  // Get current active streak
  const currentStreak = await prisma.streak.findFirst({
    where: {
      habitId,
      userId,
      isActive: true
    },
    orderBy: { startDate: 'desc' }
  })

  const yesterday = new Date(completionDate)
  yesterday.setDate(yesterday.getDate() - 1)

  if (currentStreak) {
    // Check if this completion extends the current streak
    const streakEndDate = currentStreak.endDate || currentStreak.startDate
    const daysDiff = Math.floor((completionDate.getTime() - streakEndDate.getTime()) / (1000 * 60 * 60 * 24))

    if (daysDiff <= 1) {
      // Extend current streak
      await prisma.streak.update({
        where: { id: currentStreak.id },
        data: {
          endDate: completionDate,
          length: currentStreak.length + 1,
          updatedAt: new Date()
        }
      })
    } else {
      // End current streak and start new one
      await prisma.streak.update({
        where: { id: currentStreak.id },
        data: { isActive: false }
      })

      await prisma.streak.create({
        data: {
          habitId,
          userId,
          startDate: completionDate,
          endDate: completionDate,
          length: 1,
          isActive: true
        }
      })
    }
  } else {
    // Create new streak
    await prisma.streak.create({
      data: {
        habitId,
        userId,
        startDate: completionDate,
        endDate: completionDate,
        length: 1,
        isActive: true
      }
    })
  }
}

// Helper function to recalculate streak after deletion
async function recalculateStreak(habitId: string, userId: string) {
  // This is a simplified version - in a production app you'd want more sophisticated streak recalculation
  // For now, we'll just end the current streak and let it be rebuilt with future completions
  await prisma.streak.updateMany({
    where: {
      habitId,
      userId,
      isActive: true
    },
    data: { isActive: false }
  })
}
