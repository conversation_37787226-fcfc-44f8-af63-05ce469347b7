import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { CreateHabitData, ApiResponse, HabitWithCategory } from '@/types'

// GET /api/habits - Get user's habits
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const frequency = searchParams.get('frequency')
    const isActive = searchParams.get('isActive')

    // Build where clause
    const where: any = {
      userId: session.user.id
    }

    if (frequency && ['DAILY', 'WEEKLY', 'MONTHLY'].includes(frequency)) {
      where.frequency = frequency
    }

    if (isActive !== null) {
      where.isActive = isActive === 'true'
    }

    // Fetch habits with category information
    const habits = await prisma.habit.findMany({
      where,
      include: {
        category: true,
        completions: {
          orderBy: { completedAt: 'desc' },
          take: 10 // Get last 10 completions
        },
        streaks: {
          where: { isActive: true },
          orderBy: { startDate: 'desc' },
          take: 1 // Get current active streak
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    const response: ApiResponse<HabitWithCategory[]> = {
      success: true,
      data: habits
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('Error fetching habits:', error)
    
    const response: ApiResponse = {
      success: false,
      error: 'Failed to fetch habits'
    }

    return NextResponse.json(response, { status: 500 })
  }
}

// POST /api/habits - Create a new habit
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body: CreateHabitData & { 
      category?: string
      isAiGenerated?: boolean 
    } = await request.json()

    // Validate required fields
    if (!body.title || !body.frequency) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Title and frequency are required' 
        },
        { status: 400 }
      )
    }

    // Validate frequency
    if (!['DAILY', 'WEEKLY', 'MONTHLY'].includes(body.frequency)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid frequency. Must be DAILY, WEEKLY, or MONTHLY' 
        },
        { status: 400 }
      )
    }

    // Validate difficulty if provided
    if (body.difficulty && !['EASY', 'MEDIUM', 'HARD'].includes(body.difficulty)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid difficulty. Must be EASY, MEDIUM, or HARD' 
        },
        { status: 400 }
      )
    }

    // Find or create category if provided
    let categoryId = null
    if (body.category) {
      const category = await prisma.habitCategory.findFirst({
        where: { name: body.category }
      })
      categoryId = category?.id || null
    }

    // Create the habit
    const habit = await prisma.habit.create({
      data: {
        userId: session.user.id,
        title: body.title.trim(),
        description: body.description?.trim() || null,
        frequency: body.frequency,
        difficulty: body.difficulty || null,
        categoryId,
        isAiGenerated: body.isAiGenerated || false
      },
      include: {
        category: true,
        completions: true,
        streaks: true
      }
    })

    const response: ApiResponse<HabitWithCategory> = {
      success: true,
      data: habit,
      message: 'Habit created successfully'
    }

    return NextResponse.json(response, { status: 201 })

  } catch (error) {
    console.error('Error creating habit:', error)
    
    const response: ApiResponse = {
      success: false,
      error: 'Failed to create habit'
    }

    return NextResponse.json(response, { status: 500 })
  }
}
