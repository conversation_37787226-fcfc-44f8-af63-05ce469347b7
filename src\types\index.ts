import { User, Habit, HabitCompletion, UserProfile, HabitCategory, Streak } from '@prisma/client'

// Extended types with relations
export type UserWithProfile = User & {
  profile?: UserProfile | null
}

export type HabitWithCategory = Habit & {
  category?: HabitCategory | null
  completions?: HabitCompletion[]
  streaks?: Streak[]
}

export type HabitWithDetails = Habit & {
  category?: HabitCategory | null
  completions: HabitCompletion[]
  streaks: Streak[]
  user: User
}

// Form types
export interface CreateHabitData {
  title: string
  description?: string
  frequency: 'DAILY' | 'WEEKLY' | 'MONTHLY'
  difficulty?: 'EASY' | 'MEDIUM' | 'HARD'
  categoryId?: string
}

export interface UpdateHabitData extends Partial<CreateHabitData> {
  isActive?: boolean
}

export interface UserProfileData {
  description?: string
  goals?: string
  lifestyle?: string
  preferences?: Record<string, any>
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// Habit generation types
export interface HabitGenerationRequest {
  userDescription: string
  goals?: string
  lifestyle?: string
  preferences?: {
    frequency?: 'DAILY' | 'WEEKLY' | 'MONTHLY'
    difficulty?: 'EASY' | 'MEDIUM' | 'HARD'
    categories?: string[]
  }
}

export interface GeneratedHabit {
  title: string
  description: string
  frequency: 'DAILY' | 'WEEKLY' | 'MONTHLY'
  difficulty: 'EASY' | 'MEDIUM' | 'HARD'
  category?: string
  reasoning?: string
}

// Analytics types
export interface StreakData {
  habitId: string
  habitTitle: string
  currentStreak: number
  longestStreak: number
  isActive: boolean
}

export interface HabitStats {
  totalHabits: number
  activeHabits: number
  completedToday: number
  totalCompletions: number
  averageCompletionRate: number
}

export interface ProgressData {
  date: string
  completions: number
  habits: number
}

// UI Component types
export interface NavItem {
  title: string
  href: string
  icon?: string
  disabled?: boolean
}

export interface DashboardCard {
  title: string
  value: string | number
  description?: string
  icon?: string
  trend?: {
    value: number
    isPositive: boolean
  }
}
