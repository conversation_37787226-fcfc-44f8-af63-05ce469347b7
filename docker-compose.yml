version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: momentum-ai-db
    restart: unless-stopped
    environment:
      POSTGRES_DB: momentum_ai
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - momentum-ai-network

  # Next.js Application (for production)
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: momentum-ai-app
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=********************************************/momentum_ai?schema=public
    depends_on:
      - postgres
    networks:
      - momentum-ai-network
    profiles:
      - production

volumes:
  postgres_data:

networks:
  momentum-ai-network:
    driver: bridge
