{"name": "momentum-ai", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:seed": "tsx prisma/seed.ts"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@google/generative-ai": "^0.24.1", "@prisma/client": "^6.14.0", "clsx": "^2.1.1", "next": "15.4.6", "next-auth": "^4.24.11", "prisma": "^6.14.0", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.6", "tailwindcss": "^4", "tsx": "^4.20.4", "typescript": "^5"}}