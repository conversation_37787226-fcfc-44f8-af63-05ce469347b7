'use client'

import { useState } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { UserProfileForm } from '@/components/forms/UserProfileForm'
import { HabitList } from '@/components/habits/HabitList'
import { Button } from '@/components/ui/Button'
import { HabitGenerationRequest, GeneratedHabit, ApiResponse } from '@/types'

export default function GenerateHabitsPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [generatedHabits, setGeneratedHabits] = useState<GeneratedHabit[]>([])
  const [isGenerating, setIsGenerating] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [step, setStep] = useState<'form' | 'results'>('form')

  // Redirect if not authenticated
  if (status === 'unauthenticated') {
    router.push('/')
    return null
  }

  if (status === 'loading') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  const handleGenerateHabits = async (formData: HabitGenerationRequest) => {
    setIsGenerating(true)
    setError(null)

    try {
      const response = await fetch('/api/habits/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      const result: ApiResponse<GeneratedHabit[]> = await response.json()

      if (!response.ok || !result.success) {
        throw new Error(result.error || 'Failed to generate habits')
      }

      setGeneratedHabits(result.data || [])
      setStep('results')
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
    } finally {
      setIsGenerating(false)
    }
  }

  const handleSaveHabit = async (habit: GeneratedHabit) => {
    try {
      const response = await fetch('/api/habits', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: habit.title,
          description: habit.description,
          frequency: habit.frequency,
          difficulty: habit.difficulty,
          category: habit.category,
          isAiGenerated: true
        }),
      })

      const result: ApiResponse = await response.json()

      if (!response.ok || !result.success) {
        throw new Error(result.error || 'Failed to save habit')
      }

      // Remove the saved habit from the generated list
      setGeneratedHabits(prev => prev.filter(h => h !== habit))
      
      // Show success message (you could add a toast notification here)
      console.log('Habit saved successfully!')
    } catch (err) {
      console.error('Error saving habit:', err)
      setError(err instanceof Error ? err.message : 'Failed to save habit')
    }
  }

  const handleSaveAllHabits = async (habits: GeneratedHabit[]) => {
    try {
      const savePromises = habits.map(habit =>
        fetch('/api/habits', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            title: habit.title,
            description: habit.description,
            frequency: habit.frequency,
            difficulty: habit.difficulty,
            category: habit.category,
            isAiGenerated: true
          }),
        })
      )

      const responses = await Promise.all(savePromises)
      
      // Check if all requests were successful
      const results = await Promise.all(
        responses.map(response => response.json())
      )

      const failedSaves = results.filter(result => !result.success)
      
      if (failedSaves.length > 0) {
        throw new Error(`Failed to save ${failedSaves.length} habits`)
      }

      // Clear all generated habits and redirect to dashboard
      setGeneratedHabits([])
      router.push('/dashboard')
    } catch (err) {
      console.error('Error saving habits:', err)
      setError(err instanceof Error ? err.message : 'Failed to save some habits')
    }
  }

  const handleStartOver = () => {
    setGeneratedHabits([])
    setStep('form')
    setError(null)
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Generate AI-Powered Habits
          </h1>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Tell us about yourself and let our AI create personalized habits that fit your lifestyle and goals.
          </p>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-800">{error}</p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setError(null)}
              className="mt-2"
            >
              Dismiss
            </Button>
          </div>
        )}

        {/* Content */}
        {step === 'form' ? (
          <UserProfileForm
            onSubmit={handleGenerateHabits}
            isLoading={isGenerating}
          />
        ) : (
          <div className="space-y-6">
            {/* Results Header */}
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">
                    Your Personalized Habits
                  </h2>
                  <p className="text-gray-600">
                    Our AI has generated {generatedHabits.length} habits tailored specifically for you.
                    Review them and save the ones you'd like to start tracking.
                  </p>
                </div>
                <Button
                  variant="outline"
                  onClick={handleStartOver}
                >
                  Generate New Habits
                </Button>
              </div>
            </div>

            {/* Generated Habits */}
            <HabitList
              habits={generatedHabits}
              isGenerated={true}
              onSaveHabit={handleSaveHabit}
              onSaveAll={handleSaveAllHabits}
              title="Generated Habits"
              emptyMessage="No habits were generated. Please try again with different information."
            />

            {/* Navigation */}
            {generatedHabits.length === 0 && (
              <div className="text-center">
                <Button onClick={() => router.push('/dashboard')}>
                  Go to Dashboard
                </Button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
