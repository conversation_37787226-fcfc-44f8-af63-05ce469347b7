version: '3.8'

services:
  # PostgreSQL Database for Development
  postgres:
    image: postgres:15-alpine
    container_name: momentum-ai-dev-db
    restart: unless-stopped
    environment:
      POSTGRES_DB: momentum_ai_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    networks:
      - momentum-ai-dev-network

volumes:
  postgres_dev_data:

networks:
  momentum-ai-dev-network:
    driver: bridge
