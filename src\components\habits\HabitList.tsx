'use client'

import { useState } from 'react'
import { HabitCard } from './HabitCard'
import { Button } from '@/components/ui/Button'
import { GeneratedHabit, HabitWithCategory } from '@/types'

interface HabitListProps {
  habits: (GeneratedHabit | HabitWithCategory)[]
  isGenerated?: boolean
  onSaveHabit?: (habit: GeneratedHabit) => Promise<void>
  onEditHabit?: (habit: HabitWithCategory) => void
  onDeleteHabit?: (habitId: string) => Promise<void>
  onToggleComplete?: (habitId: string) => Promise<void>
  onSaveAll?: (habits: GeneratedHabit[]) => Promise<void>
  title?: string
  emptyMessage?: string
  className?: string
}

export function HabitList({
  habits,
  isGenerated = false,
  onSaveHabit,
  onEditHabit,
  onDeleteHabit,
  onToggleComplete,
  onSaveAll,
  title,
  emptyMessage,
  className
}: HabitListProps) {
  const [filter, setFilter] = useState<'ALL' | 'DAILY' | 'WEEKLY' | 'MONTHLY'>('ALL')
  const [sortBy, setSortBy] = useState<'created' | 'frequency' | 'difficulty'>('created')
  const [isLoading, setIsLoading] = useState(false)

  // Filter habits based on frequency
  const filteredHabits = habits.filter(habit => {
    if (filter === 'ALL') return true
    return habit.frequency === filter
  })

  // Sort habits
  const sortedHabits = [...filteredHabits].sort((a, b) => {
    switch (sortBy) {
      case 'frequency':
        const freqOrder = { DAILY: 1, WEEKLY: 2, MONTHLY: 3 }
        return freqOrder[a.frequency] - freqOrder[b.frequency]
      case 'difficulty':
        const diffOrder = { EASY: 1, MEDIUM: 2, HARD: 3 }
        return diffOrder[a.difficulty] - diffOrder[b.difficulty]
      case 'created':
      default:
        if ('createdAt' in a && 'createdAt' in b) {
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        }
        return 0
    }
  })

  const handleSaveAll = async () => {
    if (!onSaveAll || !isGenerated) return
    
    setIsLoading(true)
    try {
      await onSaveAll(habits as GeneratedHabit[])
    } finally {
      setIsLoading(false)
    }
  }

  if (habits.length === 0) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <div className="text-6xl mb-4">📝</div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          {emptyMessage || 'No habits yet'}
        </h3>
        <p className="text-gray-600">
          {isGenerated 
            ? 'Generate some habits using AI to get started!'
            : 'Create your first habit to begin your journey!'
          }
        </p>
      </div>
    )
  }

  return (
    <div className={className}>
      {/* Header */}
      {title && (
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900">{title}</h2>
          {isGenerated && onSaveAll && habits.length > 0 && (
            <Button
              onClick={handleSaveAll}
              disabled={isLoading}
              className="bg-green-600 hover:bg-green-700"
            >
              {isLoading ? 'Saving All...' : `Save All ${habits.length} Habits`}
            </Button>
          )}
        </div>
      )}

      {/* Filters and Sort */}
      <div className="flex flex-wrap items-center gap-4 mb-6 p-4 bg-gray-50 rounded-lg">
        {/* Frequency Filter */}
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium text-gray-700">Filter:</span>
          <div className="flex gap-1">
            {(['ALL', 'DAILY', 'WEEKLY', 'MONTHLY'] as const).map((freq) => (
              <button
                key={freq}
                onClick={() => setFilter(freq)}
                className={`px-3 py-1 text-sm rounded-md transition-colors ${
                  filter === freq
                    ? 'bg-blue-500 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-100'
                }`}
              >
                {freq}
              </button>
            ))}
          </div>
        </div>

        {/* Sort */}
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium text-gray-700">Sort by:</span>
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as typeof sortBy)}
            className="px-3 py-1 text-sm border border-gray-300 rounded-md bg-white"
          >
            <option value="created">Created Date</option>
            <option value="frequency">Frequency</option>
            <option value="difficulty">Difficulty</option>
          </select>
        </div>

        {/* Stats */}
        <div className="ml-auto text-sm text-gray-600">
          Showing {sortedHabits.length} of {habits.length} habits
        </div>
      </div>

      {/* Habit Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {sortedHabits.map((habit, index) => (
          <HabitCard
            key={isGenerated ? index : ('id' in habit ? habit.id : index)}
            habit={habit}
            isGenerated={isGenerated}
            onSave={onSaveHabit}
            onEdit={onEditHabit}
            onDelete={onDeleteHabit}
            onToggleComplete={onToggleComplete}
          />
        ))}
      </div>

      {/* Summary Stats */}
      {habits.length > 0 && (
        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <h3 className="font-medium text-blue-900 mb-2">Summary</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-blue-700">Total Habits:</span>
              <span className="ml-2 font-medium">{habits.length}</span>
            </div>
            <div>
              <span className="text-blue-700">Daily:</span>
              <span className="ml-2 font-medium">
                {habits.filter(h => h.frequency === 'DAILY').length}
              </span>
            </div>
            <div>
              <span className="text-blue-700">Weekly:</span>
              <span className="ml-2 font-medium">
                {habits.filter(h => h.frequency === 'WEEKLY').length}
              </span>
            </div>
            <div>
              <span className="text-blue-700">Monthly:</span>
              <span className="ml-2 font-medium">
                {habits.filter(h => h.frequency === 'MONTHLY').length}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
